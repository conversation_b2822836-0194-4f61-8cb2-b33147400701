use crate::protocols::bonk::{
    pda::get_associated_token_address,
    types::{BonkTradingData, TradeType, HotPathTrade},
};
use solana_sdk::pubkey::Pubkey;
use std::str::FromStr;
use uuid::Uuid;
use anyhow::Result;

/// 解析Redis中的bonk交易数据，返回HotPathTrade和原始BonkTradingData
pub fn parse_bonk_trades_from_redis_bytes(
    payload: &[u8], 
    user_wallet_pubkey: &Pubkey
) -> Vec<(HotPathTrade, BonkTradingData)> {
    // 首先将字节转换为字符串
    let encoded_str = match std::str::from_utf8(payload) {
        Ok(s) => s,
        Err(_) => return Vec::new(),
    };

    // 尝试base64解码
    let decoded_text = match base64::engine::general_purpose::STANDARD.decode(encoded_str) {
        Ok(decoded_bytes) => {
            match std::str::from_utf8(&decoded_bytes) {
                Ok(text) => text.to_string(),
                Err(_) => return Vec::new(),
            }
        },
        Err(_) => {
            // 如果base64解码失败，可能是纯文本格式
            encoded_str.to_string()
        }
    };

    // 快速解析文本格式为BonkTradingData
    let trading_data = match parse_text_format_fast(&decoded_text) {
        Some(data) => data,
        None => return Vec::new(),
    };

    // 跳过未知Token地址的消息
    if trading_data.mint_address == "unknown" || trading_data.mint_address.is_empty() {
        return Vec::new();
    }

    // 转换为热路径交易数据
    match convert_to_hot_path_trade(&trading_data, user_wallet_pubkey) {
        Ok(hot_path_trade) => vec![(hot_path_trade, trading_data)],
        Err(_) => Vec::new(),
    }
}

/// 将BonkTradingData转换为通用的HotPathTrade
fn convert_to_hot_path_trade(trading_data: &BonkTradingData, user_wallet_pubkey: &Pubkey) -> Result<HotPathTrade> {
    // 解析基础地址
    let mint_pubkey = Pubkey::from_str(&trading_data.mint_address)?;
    let pool_state = Pubkey::from_str(&trading_data.pool_state)?;
    let pool_base_vault = Pubkey::from_str(&trading_data.pool_base_vault)?;
    let pool_quote_vault = Pubkey::from_str(&trading_data.pool_quote_vault)?;

    // 计算PDA地址（bonk特有的创作者金库地址，这里使用pool_state作为代替）
    let creator_vault_pubkey = pool_state; // bonk使用pool state作为主要标识

    // 计算债券曲线地址（bonk使用pool_base_vault）
    let bonding_curve_pubkey = pool_base_vault;
    let associated_bonding_curve = pool_quote_vault;

    // 计算用户Token账户
    let user_ata = get_associated_token_address(user_wallet_pubkey, &mint_pubkey);

    // 确定交易类型
    let trade_type = match trading_data.trade_direction.as_str() {
        "Buy" => TradeType::Buy,
        "Sell" => TradeType::Sell,
        _ => TradeType::Unknown,
    };

    // 计算价格
    let price = if trading_data.amount_in > 0 {
        trading_data.amount_out as f64 / trading_data.amount_in as f64
    } else {
        0.0
    };

    Ok(HotPathTrade {
        trade_id: Uuid::new_v4().to_string(),
        trade_type,
        signature: trading_data.signature.clone(),
        sol_cost: trading_data.amount_in as f64 / 1_000_000_000.0, // lamports转SOL
        token_amount: trading_data.amount_out,
        signer: trading_data.signer.clone(),
        price,
        mint_pubkey,
        creator_vault_pubkey,
        bonding_curve_pubkey,
        associated_bonding_curve,
        user_ata,
        slippage_bps: 500, // 默认5%滑点
    })
}

/// 创建Bonk买入指令数据
pub fn create_bonk_buy_instruction_data(amount_in: u64, minimum_amount_out: u64) -> Vec<u8> {
    let mut data = vec![250, 234, 13, 123, 213, 156, 19, 236]; // buy_exact_in指令discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
    data
}

/// 创建Bonk卖出指令数据
pub fn create_bonk_sell_instruction_data(amount_in: u64, minimum_amount_out: u64) -> Vec<u8> {
    let mut data = vec![149, 39, 222, 155, 211, 124, 152, 26]; // sell_exact_in指令discriminator
    data.extend_from_slice(&amount_in.to_le_bytes());
    data.extend_from_slice(&minimum_amount_out.to_le_bytes());
    data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
    data
}

/// 高性能文本格式解析器 - 零分配解析
fn parse_text_format_fast(text: &str) -> Option<BonkTradingData> {
    let mut data = BonkTradingData::default();

    // 调试：显示输入数据的前100个字符
    tracing::debug!("解析输入数据前100字符: {:?}", &text.chars().take(100).collect::<String>());

    // 使用字节切片避免字符串分配
    let bytes = text.as_bytes();
    let mut i = 0;
    
    while i < bytes.len() {
        // 快速跳过到字段名开始
        while i < bytes.len() && bytes[i] != b'"' {
            i += 1;
        }
        if i >= bytes.len() { break; }
        i += 1; // 跳过开始引号
        
        let field_start = i;
        // 找到字段名结束
        while i < bytes.len() && bytes[i] != b'"' {
            i += 1;
        }
        if i >= bytes.len() { break; }
        
        let field_name = unsafe { std::str::from_utf8_unchecked(&bytes[field_start..i]) };
        
        // 跳过到值开始 ": "
        i += 1; // 跳过结束引号
        while i < bytes.len() && bytes[i] != b':' {
            i += 1;
        }
        if i >= bytes.len() { break; }
        i += 1; // 跳过冒号
        while i < bytes.len() && (bytes[i] == b' ' || bytes[i] == b'\t') {
            i += 1;
        }
        
        let mut value_start = i;
        let mut value_end = i;
        
        // 根据字段类型解析值
        match field_name {
            "signature" | "pool_state" | "signer" | "mint_address" | 
            "trade_direction" | "pool_status" | "pool_base_vault" | "pool_quote_vault" => {
                // 字符串值，跳过开始引号
                if i < bytes.len() && bytes[i] == b'"' {
                    i += 1;
                    value_start = i;
                    while i < bytes.len() && bytes[i] != b'"' {
                        i += 1;
                    }
                    value_end = i;
                    
                    let value = unsafe { std::str::from_utf8_unchecked(&bytes[value_start..value_end]) };
                    match field_name {
                        "signature" => data.signature = value.to_string(),
                        "pool_state" => data.pool_state = value.to_string(),
                        "signer" => data.signer = value.to_string(),
                        "mint_address" => data.mint_address = value.to_string(),
                        "trade_direction" => data.trade_direction = value.to_string(),
                        "pool_status" => data.pool_status = value.to_string(),
                        "pool_base_vault" => data.pool_base_vault = value.to_string(),
                        "pool_quote_vault" => data.pool_quote_vault = value.to_string(),
                        _ => {}
                    }
                }
            }
            _ => {
                // 数值类型
                while i < bytes.len() && 
                      (bytes[i].is_ascii_digit() || bytes[i] == b'.' || bytes[i] == b'-' || bytes[i] == b'e' || bytes[i] == b'E') {
                    i += 1;
                }
                value_end = i;
                
                let value_str = unsafe { std::str::from_utf8_unchecked(&bytes[value_start..value_end]) };
                
                match field_name {
                    "total_base_sell" => data.total_base_sell = value_str.parse().unwrap_or(0),
                    "virtual_base" => data.virtual_base = value_str.parse().unwrap_or(0),
                    "virtual_quote" => data.virtual_quote = value_str.parse().unwrap_or(0),
                    "real_base_before" => data.real_base_before = value_str.parse().unwrap_or(0),
                    "real_quote_before" => data.real_quote_before = value_str.parse().unwrap_or(0),
                    "real_base_after" => data.real_base_after = value_str.parse().unwrap_or(0),
                    "real_quote_after" => data.real_quote_after = value_str.parse().unwrap_or(0),
                    "amount_in" => data.amount_in = value_str.parse().unwrap_or(0),
                    "amount_out" => data.amount_out = value_str.parse().unwrap_or(0),
                    "protocol_fee" => data.protocol_fee = value_str.parse().unwrap_or(0),
                    "platform_fee" => data.platform_fee = value_str.parse().unwrap_or(0),
                    "share_fee" => data.share_fee = value_str.parse().unwrap_or(0),
                    "price_before" => data.price_before = value_str.parse().unwrap_or(0.0),
                    "price_after" => data.price_after = value_str.parse().unwrap_or(0.0),
                    "slippage" => data.slippage = value_str.parse().unwrap_or(0.0),
                    _ => {}
                }
            }
        }
        
        // 跳到下一行
        while i < bytes.len() && bytes[i] != b'\n' {
            i += 1;
        }
        i += 1; // 跳过换行符
    }
    
    // 验证必需字段
    if data.signature.is_empty() || data.mint_address.is_empty() || data.signer.is_empty() {
        return None;
    }
    
    Some(data)
}