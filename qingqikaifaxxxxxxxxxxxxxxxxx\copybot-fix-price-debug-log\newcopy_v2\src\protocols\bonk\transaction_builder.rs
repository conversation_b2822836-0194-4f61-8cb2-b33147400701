use crate::{
    protocols::bonk::{
        pda::{find_launchpad_authority, find_event_authority, get_associated_token_address},
        types::{BonkTradingData, RAYDIUM_LAUNCHPAD_PROGRAM_ID, WSOL_MINT},
    },
    shared::types::WalletConfig,
    blockhash_service::BlockhashService,
};
use solana_sdk::{
    instruction::{AccountMeta, Instruction},
    message::Message,
    pubkey::Pubkey,
    signature::{Keypair, Signer},
    system_instruction,
    compute_budget::ComputeBudgetInstruction,
    transaction::Transaction,
};
use std::{sync::Arc, str::FromStr};
use anyhow::{anyhow, Result};
use tracing::debug;

/// Bonk协议交易构建器
/// 基于参考代码实现Raydium Launchpad交易构建
pub struct BonkTransactionBuilder {
    wallet: Arc<Keypair>,
    blockhash_service: Arc<BlockhashService>,
}

impl BonkTransactionBuilder {
    pub fn new(wallet: Arc<Keypair>, blockhash_service: Arc<BlockhashService>) -> Self {
        Self {
            wallet,
            blockhash_service,
        }
    }

    /// 构建Bonk买入交易
    pub async fn build_buy_transaction(
        &self,
        trading_data: &BonkTradingData,
        buy_amount_sol: f64,
        wallet_config: &WalletConfig,
    ) -> Result<Transaction> {
        let wsol_amount = (buy_amount_sol * 1_000_000_000.0) as u64; // SOL转lamports
        
        // 基础参数
        let pool_id = Pubkey::from_str(&trading_data.pool_state)?;
        let token_mint = Pubkey::from_str(&trading_data.mint_address)?;
        let base_vault = Pubkey::from_str(&trading_data.pool_base_vault)?;
        let quote_vault = Pubkey::from_str(&trading_data.pool_quote_vault)?;
        
        // 计算PDA地址
        let (authority, _) = find_launchpad_authority();
        let (event_authority, _) = find_event_authority();
        
        // 固定配置地址（来自参考代码）
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        // 用户账户地址
        let user_wsol_account = get_associated_token_address(&self.wallet.pubkey(), &WSOL_MINT);
        let user_token_account = get_associated_token_address(&self.wallet.pubkey(), &token_mint);
        
        // 计算期望输出（基于Redis数据的比例）
        let minimum_amount_out = if trading_data.amount_out > 0 {
            let ratio = wsol_amount as f64 / trading_data.amount_in as f64;
            let expected_out = ratio * trading_data.amount_out as f64;
            (expected_out * 0.95) as u64 // 5%滑点保护
        } else {
            1
        };
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算
        let compute_limit = if wallet_config.compute_unit_limit > 0 {
            wallet_config.compute_unit_limit
        } else {
            600_000 // 参考代码默认值
        };
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(compute_limit));
        
        let priority_fee = if wallet_config.priority_fee > 0 {
            wallet_config.priority_fee
        } else {
            100_000 // 参考代码默认值
        };
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(priority_fee));
        
        // 2. 创建Token账户（幂等操作）
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &WSOL_MINT,
                &spl_token::id(),
            )
        );
        
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &token_mint,
                &spl_token::id(),
            )
        );
        
        // 3. SOL转到WSOL账户
        instructions.push(system_instruction::transfer(
            &self.wallet.pubkey(),
            &user_wsol_account,
            wsol_amount,
        ));
        
        // 4. 同步WSOL账户
        instructions.push(spl_token::instruction::sync_native(&spl_token::id(), &user_wsol_account)?);
        
        // 5. 构建Raydium Launchpad买入指令
        let mut swap_data = vec![250, 234, 13, 123, 213, 156, 19, 236]; // buy_exact_in指令discriminator
        swap_data.extend_from_slice(&wsol_amount.to_le_bytes()); // amount_in
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes()); // minimum_amount_out  
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 构建Raydium Launchpad 15个账户列表
        let accounts = vec![
            // 0. payer (user wallet)
            AccountMeta::new_readonly(self.wallet.pubkey(), true),
            // 1. authority (PDA)
            AccountMeta::new_readonly(authority, false),
            // 2. global_config
            AccountMeta::new_readonly(global_config, false),
            // 3. platform_config
            AccountMeta::new_readonly(platform_config, false),
            // 4. pool_state
            AccountMeta::new(pool_id, false),
            // 5. user_base_token (用户Token账户)
            AccountMeta::new(user_token_account, false),
            // 6. user_quote_token (用户WSOL账户)
            AccountMeta::new(user_wsol_account, false),
            // 7. base_vault
            AccountMeta::new(base_vault, false),
            // 8. quote_vault
            AccountMeta::new(quote_vault, false),
            // 9. base_token_mint (Token mint)
            AccountMeta::new_readonly(token_mint, false),
            // 10. quote_token_mint (WSOL mint)
            AccountMeta::new_readonly(WSOL_MINT, false),
            // 11. base_token_program (SPL Token)
            AccountMeta::new_readonly(spl_token::id(), false),
            // 12. quote_token_program (SPL Token)
            AccountMeta::new_readonly(spl_token::id(), false),
            // 13. event_authority (PDA)
            AccountMeta::new_readonly(event_authority, false),
            // 14. program (Raydium Launchpad)
            AccountMeta::new_readonly(RAYDIUM_LAUNCHPAD_PROGRAM_ID, false),
        ];
        
        instructions.push(Instruction {
            program_id: RAYDIUM_LAUNCHPAD_PROGRAM_ID,
            accounts,
            data: swap_data,
        });
        
        // 6. 买入成功后立即关闭WSOL账户赎回租金
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.wallet.pubkey(), // 租金接收者
            &self.wallet.pubkey(), // 账户所有者
            &[],
        )?);
        
        // 创建交易
        let blockhash_data = self
            .blockhash_service
            .get_latest_blockhash()
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;
        
        let message = Message::new(&instructions, Some(&self.wallet.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&*self.wallet], blockhash_data.hash);
        
        debug!("构建Bonk买入交易: Token={}, SOL={}", token_mint, buy_amount_sol);
        
        Ok(transaction)
    }

    /// 构建Bonk卖出交易
    pub async fn build_sell_transaction(
        &self,
        trading_data: &BonkTradingData,
        token_amount: u64,
        wallet_config: &WalletConfig,
    ) -> Result<Transaction> {
        // 基础参数
        let pool_id = Pubkey::from_str(&trading_data.pool_state)?;
        let token_mint = Pubkey::from_str(&trading_data.mint_address)?;
        let base_vault = Pubkey::from_str(&trading_data.pool_base_vault)?;
        let quote_vault = Pubkey::from_str(&trading_data.pool_quote_vault)?;
        
        // 计算PDA地址
        let (authority, _) = find_launchpad_authority();
        let (event_authority, _) = find_event_authority();
        
        // 固定配置地址
        let global_config = Pubkey::from_str("6s1xP3hpbAfFoNtUNF8mfHsjr2Bd97JxFJRWLbL6aHuX")?;
        let platform_config = Pubkey::from_str("FfYek5vEz23cMkWsdJwG2oa6EphsvXSHrGpdALN4g6W1")?;
        
        // 用户账户地址
        let user_wsol_account = get_associated_token_address(&self.wallet.pubkey(), &WSOL_MINT);
        let user_token_account = get_associated_token_address(&self.wallet.pubkey(), &token_mint);
        
        // 计算期望输出（基于Redis数据的比例）
        let minimum_amount_out = if trading_data.amount_in > 0 && trading_data.amount_out > 0 {
            let ratio = token_amount as f64 / trading_data.amount_in as f64;
            let expected_out = ratio * trading_data.amount_out as f64;
            (expected_out * 0.95) as u64 // 5%滑点保护
        } else {
            1
        };
        
        let mut instructions = Vec::new();
        
        // 1. 设置计算预算
        let compute_limit = wallet_config.sell_compute_unit_limit
            .unwrap_or(wallet_config.compute_unit_limit);
        let compute_limit = if compute_limit > 0 { compute_limit } else { 600_000 };
        instructions.push(ComputeBudgetInstruction::set_compute_unit_limit(compute_limit));
        
        let priority_fee = wallet_config.sell_priority_fee
            .unwrap_or(wallet_config.priority_fee);
        let priority_fee = if priority_fee > 0 { priority_fee } else { 100_000 };
        instructions.push(ComputeBudgetInstruction::set_compute_unit_price(priority_fee));
        
        // 2. 创建WSOL账户（如果不存在）
        instructions.push(
            spl_associated_token_account::instruction::create_associated_token_account_idempotent(
                &self.wallet.pubkey(),
                &self.wallet.pubkey(),
                &WSOL_MINT,
                &spl_token::id(),
            )
        );
        
        // 3. 构建Raydium Launchpad卖出指令
        let mut swap_data = vec![149, 39, 222, 155, 211, 124, 152, 26]; // sell_exact_in指令discriminator
        swap_data.extend_from_slice(&token_amount.to_le_bytes()); // amount_in (Token数量)
        swap_data.extend_from_slice(&minimum_amount_out.to_le_bytes()); // minimum_amount_out  
        swap_data.extend_from_slice(&0u64.to_le_bytes()); // share_fee_rate
        
        // 构建账户列表（卖出顺序）
        let accounts = vec![
            // 0. payer (user wallet)
            AccountMeta::new_readonly(self.wallet.pubkey(), true),
            // 1. authority (PDA)
            AccountMeta::new_readonly(authority, false),
            // 2. global_config
            AccountMeta::new_readonly(global_config, false),
            // 3. platform_config
            AccountMeta::new_readonly(platform_config, false),
            // 4. pool_state
            AccountMeta::new(pool_id, false),
            // 5. user_base_token (用户Token账户) - 卖出源
            AccountMeta::new(user_token_account, false),
            // 6. user_quote_token (用户WSOL账户) - 接收WSOL
            AccountMeta::new(user_wsol_account, false),
            // 7. base_vault
            AccountMeta::new(base_vault, false),
            // 8. quote_vault
            AccountMeta::new(quote_vault, false),
            // 9. base_token_mint (Token mint)
            AccountMeta::new_readonly(token_mint, false),
            // 10. quote_token_mint (WSOL mint)
            AccountMeta::new_readonly(WSOL_MINT, false),
            // 11. base_token_program (SPL Token)
            AccountMeta::new_readonly(spl_token::id(), false),
            // 12. quote_token_program (SPL Token)
            AccountMeta::new_readonly(spl_token::id(), false),
            // 13. event_authority (PDA)
            AccountMeta::new_readonly(event_authority, false),
            // 14. program (Raydium Launchpad)
            AccountMeta::new_readonly(RAYDIUM_LAUNCHPAD_PROGRAM_ID, false),
        ];
        
        instructions.push(Instruction {
            program_id: RAYDIUM_LAUNCHPAD_PROGRAM_ID,
            accounts,
            data: swap_data,
        });
        
        // 4. 卖出成功后unwrap WSOL为SOL
        instructions.push(spl_token::instruction::sync_native(&spl_token::id(), &user_wsol_account)?);
        
        // 5. 立即关闭WSOL账户赎回租金
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_wsol_account,
            &self.wallet.pubkey(),
            &self.wallet.pubkey(),
            &[],
        )?);
        
        // 6. 关闭空的Token账户赎回租金
        instructions.push(spl_token::instruction::close_account(
            &spl_token::id(),
            &user_token_account,
            &self.wallet.pubkey(),
            &self.wallet.pubkey(),
            &[],
        )?);
        
        // 创建交易
        let blockhash_data = self
            .blockhash_service
            .get_latest_blockhash()
            .ok_or_else(|| anyhow!("无法获取最新的区块哈希"))?;
        
        let message = Message::new(&instructions, Some(&self.wallet.pubkey()));
        let mut transaction = Transaction::new_unsigned(message);
        transaction.sign(&[&*self.wallet], blockhash_data.hash);
        
        debug!("构建Bonk卖出交易: Token={}, Amount={}", token_mint, token_amount);
        
        Ok(transaction)
    }
}